# 绪论

## 1.1论文背景及意义

专利文献作为技术创新的核心载体，是现代科技和知识体系的核心组成部分， 具有内容繁杂、格式多样的特点。随着全球专利体系的发展和科技水平的迅猛提升，专利文献资源的数量显著增长，全球年增长率已达 5%-7% ~\cite{WIPO2024}。化学专利文献中的化学结构信息直接反应了新型化合物，药物分子、反应路线等核心技术，对于药物合成路线提取、药品性质分析和逆合成路线设计具有重要的研究价值。

化学领域涉及大量的文档和数据信息，其中包括各种不同类型的文档，例如以电子文档形式存储的EXCEL表格，可以直接解析的PDF文件，WORD文档和以扫描件存在的视觉富文本文档。如图1.1所示，由于文档信息的多样性、内容的复杂性及其非结构化特征，信息检索与布局检测成为一项极具挑战的任务。对于这些文档，传统的人工提取方法效率底下且容易出错，同时缺乏对错误来源的追踪。因此，如何高效地对化学专利文献进行解析，以实现化学结构信息的自动识别、提取与标准化成为亟待解决的问题。

近年来，随着计算机视觉、深度学习和语言大模型的发展，自动化提取专利中的化学结构信息成为可能。然而，现有的化学结构识别系统仍面临诸多挑战：一是识别准确率不足，特别是对于复杂结构、低质量图像和特殊表示法；二是处理效率低下，难以应对大规模专利文献；三是缺乏完整的工作流程，从文档处理到结果管理的全流程支持不足；四是用户交互体验差，专业人员使用门槛高。
## 1.2国内外研究现状
本文主要关注文档信息的提取和光学化学结构识别（OCSR），文档信息处理指文本和图像内容，化学结构即化学反应式、分子式提取和SMILES表示。本节内容将对这两方面的国内外研究进行简要总结。
### 1.2.1文档内容提取
文档内容提取（也称文档解析）旨在将各种非结构化或半结构化的文档（如专利文献、开放数据及企业资料）转换为结构化信息，自动识别并抽取其中的文本、公式、表格、图像等内容，同时保留其空间和逻辑关系。随着人工智能和文档处理技术的发展，当前主流的技术范式主要包括：基于OCR的文本提取、基于库的文本解析、多模块流水线式解析，以及基于端到端多模态大语言模型的解析方法。

传统OCR系统通常采用模块化设计范式，包含图像预处理、文本区域检测、字符分割、特征提取、分类器规划和图像后处理等多个独立功能模块。例如，经典的Tesseract OCR引擎先进行连通域分析和字符轮廓提取，然后按行分割成文本行，再执行自适应分类器迭代识别等多个阶段~\cite{smith2007overview}。这类方法依赖手工设计的特征和模板匹配算法，能够在印刷质量较好、版面较简单的文档中取得较高识别率。但当文档版面结构复杂（如多栏布局、跨页表格、嵌套公式等）时，这些逐行识别的方法往往无法正确理解上下文关系，导致识别误差显著增加~\cite{blecher2023nougat}。例如，Tesseract等传统OCR对数学公式中的上下标符号关系处理不佳，经常将公式中的上标或下标与正文文本混淆~\cite{blecher2023nougat}，从而影响提取精度。

对于数字原生文档（即非扫描产生的PDF、XML等文件），可直接利用解析库对文档内容流进行解析，从而高效提取文本和结构信息。开源工具如PyMuPDF和PDFMiner能够直接基于PDF文件格式规范（如PDF2.0标准）解析内容流，提供更高效、更准确的文本提取结果。此类方法的优势在于处理速度快、结构保留度高，但其局限性体现在无法识别扫描文档中的图像化内容，例如手写公式、化学结构式等非标准化元素。

多模块文档解析技术采用分阶段处理范式，通过将文档解析任务分解为多个功能独立的子模块，如图像识别、图像标题提取、表格检测与解析、表格标题提取和正文文本提取等，以流水线方式实现从原始文档到结构化数据的转换。在这种架构下，每个模块完成各自任务后将结果作为下一阶段的输入，从而实现从原始文档到结构化数据的转换。理论上，这种“分解-集成”的方法便于对每个步骤单独优化，在一定程度上能获得高质量的抽取结果。然而，流水线架构使得模块间耦合性较高，容易导致错误传播问题，同时，多阶段的串行处理流程也会增加整体推理延迟。在实践中，这意味着一方面需要精心设计各模块接口以降低误差传播；另一方面整体系统的实时性能较差，不利于对海量文档进行快速批处理。

随着视觉-语言大模型（如GPT-4V、QwenVL、InternVL等）的出现，研究者开始探索端到端的文档解析方案。这些统一模型能够同时接受图像和文本输入，将多种文档元素（印刷文本、手写文本、表格、公式、图像等）一并理解并输出结构化表示。近年来，诸如 Nougat、Fox、Vary、GOT等多模态大模型在文档解析任务上取得了显著进展~\{zhang2024document}。例如，Fox模型通过融合多模态信息，增强了对文档语义的理解能力；而GOT模型采用统一架构，可以处理包括文本、表格、数学公式乃至化学分子结构和几何图形等多种内容~\{zhang2024document}。

尽管如此，多模态大模型也面临挑战：首先，此类模型通常规模庞大，需要在海量多模态文档上进行预训练或指令微调，训练数据的多样性和质量直接影响性能；目前适用于专业文档（如专利、学术论文等）的标注数据仍相对有限。其次，大规模视觉-语言模型的模型尺寸和训练推理成本极高，这在学术界和工业界都阻碍了它们的广泛应用\{jin2024efficient}。因此，虽然端到端多模态解析代表了文档内容提取的前沿方向，但在实际工程实践中两阶段（检测+识别）的应用更为广泛。
### 1.2.2化学结构识别
早期的化学识别主要通过规则进行图像处理，Kekule~\cite{mcdaniel1992kekule}是这一领域的代表性工作，处理流程为扫描、矢量化、虚线和楔形线检索识别化学结构图中的原子和键。这类方法支持对常见基团缩写、虚线和楔形线的识别，但对图像质量要求高，难以处理结构变形、部分遮挡和特殊表示法等情况。

随着深度学习在图像领域的快速发展，基于深度学习的方法不再依靠人工设计的特征工程，模型可以自动从数据中学习化学结构，并降低了对图像质量的敏感性。杨赵朋~\{杨赵朋2023deepocsr}提出了基于Transformer和ResNeSt模型，通过编码器-解码器架构的化学结构识别系统。YujieQian~\{qian2023molscribe}提出了一种图到图的生成模型（image-to-graph generation），将分子图像编码成特征向量，再用图解码器根据特征自回归地生成分子图结构。这些基于深度学习的方法大幅提高了识别准确率，但对训练数据的依赖较强，且对计算资源需求较大。

这些研究的成果在文档信息抽取任务中取得了不错的成果，但现有系统缺乏完整的工作流程，用户需要在多个工具之前切换，增加了操作复杂性。此外，现有系统大多采用单机架构，难以拓展应对负载增加，随着专利数量的不断增长，分布式架构成为必然选择。
## 1.3主要研究内容
面对化学专利文献中数字文本样式多样，专利文献数量庞大的问题，本研究提出了一种基于分布式架构的专利文献化学式自动提取与分析系统。实现了对专利文档的高效解析与对化学反应式的精确识别。内容如下：

1. 针对化学专利文献中的数字原生文档格式多样的问题，提出了一个基于解析库的文本提取框架。该框架采用直接解析文档内容流的方法，避免对原生数字文件进行二次转换。有效提高了Excel、Word和PPT解析的准确性与结构保留程度。通过分析文档对象模型，研究实现了对文档中文本、表格、图像等元素的精准提取和Markdown格式转换。
2. 本文构建了基于FastAPI和LitServe的一套轻量级分布式微服务系统架构。该系统将文档解析、化学反应识别等模块解耦于多个独立服务，采用多进程并发架构并支持负载均衡。本地后端服务作为协调器，实现服务集成与业务流程控制。每个微服务专注于特定的业务功能，通过HTTP协议通信，降低了系统复杂度，具备良好的松耦合性和可拓展性，为专利化学信息的自动化处理提供了新的解决方案。
3. 本文对RxnScribe的OCR模块进行了优化升级，引入效果更好的PaddleOCR技术，提高了对化学反应图像中反应条件文本的识别精度。针对识别结果，设计了结构化提取与可视化方法，将复杂的反应式识别结果转换为标注了反应顺序的EXCEL表格，便于化学研究者对照和后续分析。
4. 最后，本文探索了LLM（大型语言模型）在专利文档分析中的应用。通过精心设计的提示词模板，实现了OCR结果的智能优化，专利内容信息提取。将大模型API集成到系统工作流中，有效提升了系统的智能化水平与用户体验。
![[image/图1.svg]]


## 1.4论文篇章结构
本论文的篇章结构安排如下：
- **第一章：绪论。** 本章首先阐述化学专利文献信息提取的研究背景、动机及其在现代化学与医药研发领域的核心价值。接着，系统综述国内外在文档图像信息提取、化学结构识别以及相关系统构建等领域的研究进展与技术现状。在此基础上，深入剖析当前技术方案存在的局限性与面临的挑战，进而明确本论文的主要研究内容、拟解决的关键科学与技术问题、预期的核心贡献，并概述论文的整体篇章结构。
  
- **第二章：相关理论与关键技术。** 本章旨在为后续系统的设计与实现奠定理论与技术基础。重点讲述OCR的基本原理与前沿进展；探讨基于Transformer架构的深度学习模型在化学结构图像识别任务中的应用；介绍微服务架构的设计理念、核心原则及其在构建可扩展、高可用系统中的优势；最后，对FastAPI、LitServer等高性能Web服务框架进行技术特性分析，并阐明其在本研究中作为构建微服务的选型依据。
  
- **第三章：系统需求分析。** 本章以实际应用为导向，对智能化学信息提取系统的需求进行全面而细致的分析。通过对潜在用户群体（例如，化学制药企业研发人员、高校及科研院所研究人员）的典型应用场景调研，结合化学专利文献的处理流程，系统地梳理并明确了系统的核心功能需求，主要包括文档解析与格式转换、化学结构信息（如分子式、反应式）识别、关键化学反应条件提取以及结构化信息的可视化展示。同时，本章亦从可扩展性、安全性、性能效率及用户友好性等维度，详细论述了系统的非功能性需求指标。
  
- **第四章：系统概要设计。** 在系统需求分析的指引下，本章致力于构建智能化学信息提取系统的宏观架构。首先，提出一种基于微服务理念的轻量级分布式系统总体架构，阐明其设计思想与优势。其次，对系统的核心组成模块进行划分，重点阐述文档解析微服务、化学信息提取微服务以及可视化展示前端模块的功能定位、职责边界、模块间的接口规约、关键交互流程与核心数据流转路径，为后续的详细设计与实现提供清晰的指导框架。
  
- **第五章：系统详细设计与实现。** 本章依据概要设计阶段确立的架构方案与模块功能，深入到各个核心模块内部，详细阐述其具体的设计思路与实现细节。首先，详述基于LitServer框架的文档解析与转换微服务的内部构造，包括对MinerU的集成与优化策略，以及为提升处理效率而设计的多GPU并行处理机制。其次，重点介绍一种融合异构OCR引擎（RxnScribe与PaddleOCR）的优化方案，旨在实现化学反应方程式SMILES表示的精确提取与高置信度反应条件的有效关联。最后，详细阐述系统前端用户界面的设计理念与交互逻辑，以及后端API接口的具体规范，并通过核心算法的伪代码、关键数据结构设计和系统运行流程图等形式，直观展示核心功能的具体实现方法与技术考量。
  
- **第六章：系统测试与结果分析。** 为确保智能化学信息提取系统的功能完备性、性能稳定性及用户体验的良好性，本章对所开发的系统进行了全面而严格的测试。首先，制定了详尽的测试计划，包括测试方法的选择、测试环境的搭建配置、代表性测试数据的选取与制备，以及覆盖核心功能的测试用例设计。其次，从功能符合度测试、系统性能基准测试、模块间集成测试以及用户场景下的接收度测试等多个层面，对系统进行系统性的评估与验证。最后，通过对真实化学专利文献案例的端到端测试，展示系统的整体功能表现与各项性能指标，并针对测试过程中发现的问题进行分析，提出相应的修正对策与进一步的优化建议。
  
- **第七章：总结与展望。** 本章对整个研究工作进行全面的回顾与总结。归纳本论文在化学专利文献智能信息提取方面所取得的主要研究成果与核心技术贡献，评估研究成果的理论意义与潜在的实际应用前景。同时，分析当前研究工作中存在的不足与尚待完善之处，并在此基础上对未来可能的研究方向、技术改进路径以及系统功能的拓展进行了展望。

# 第二章 相关理论与关键技术
## 2.1基于深度学习的OCR技术
光学字符识别（Optical Character Recognition, OCR）定义了以机械或电子方式将手打、打字或打印文本的扫描图像转换为计算机可编辑文本的技术。传统OCR主要基于模式匹配和特征提取的方法。包含图像预处理、文本区域检测、字符分割、特征提取、分类器规划和图像后处理等多个独立功能模块。
随着深度学习的兴起，基于深度神经网络的OCR技术展现出对复杂场景的优越适应性，能够直接从原始数据中学习有效的特征表示。从系统架构上看，基于深度学习的OCR任务通常由三个基础框架组成：图像预处理、文本检测和文本识别。如图2.1所示。其中预处理部分包括图像去噪、二值化、倾斜校正等操作，目的是提高图像质量，便于后续处理。下面主要对文本检测和文本识别算法进行介绍。
![[论文图/图2.1.OCR处理流程图.png]]
### 2.1.2 文本检测

文本检测旨在定位输入图像或视频流中的文字区域。鉴于文本亦可被视为图像中的一类特殊目标，通用的目标检测框架同样适用于文本检测任务。然而，相较于常规的目标检测问题，文本检测具有其固有的特殊性，主要体现在以下几个方面：
1. 文本实例的视觉异构性：文本在视觉表征上呈现高度的多样性。这不仅体现在不同语言体系下字符形态的显著差异，更涵盖了字体、尺度、颜色、对比度以及排列方向（如水平、倾斜、弯曲）等多种视觉属性的复杂变化。
2. 输入图像的质量退化：实际应用中，输入图像常受到各类环境噪声的干扰，例如图像失真、模糊、低分辨率、光照不均（过曝或欠曝）以及部分遮挡等，这些因素均对文本的清晰呈现和准确识别构成挑战。
3. 复杂背景的干扰与混淆：文本所处的背景环境往往较为复杂，可能包含与待检测文本在视觉上相似的干扰元素，如纹理、图案或其他非目标文字。此外，文本实例之间也可能发生重叠，这显著增加了从背景中准确分割文本区域（尤其是基于连通域分析的方法）的难度。
4. 局部语义的完整性：单个字符可独立承载语义信息，而文本行又需维持空间连续性。这种双重属性使得检测算法在面临复杂布局或字符间距不均时，容易产生文本行断裂或字符级别漏检、误检等碎片化预测结果。
针对以上问题，学术界对文本检测的研究层出不穷，衍生出许多基于深度学习的检测算法。这些算法可以大致分为两类：基于回归的文本检测方法和基于分割的文本检测算法。
![[论文图/图2.2.文本检测算法.png]]

### 2.1.3基于回归的文本检测算法
与目标检测算法相似，基于回归的文本检测算法可以视作只有两个类别的目标检测算法，一个类别是待检测文字，另一个类别是背景。这类方法直接预测文本区域的位置和形状参数。根据适用文本的不同情况，基于回归的文本检测方法可分为水平文本检测、任意角度文本检测和弯曲文本检测。
水平文本检测算法由已经成熟的目标检测算法改进而来。这些算法通常会预先定义一系列具有不同尺寸和宽高比的锚框（Anchor Boxes），也称为先验框或默认框，均匀地分布在图像的不同位置。网络学习预测这些锚框与真实文本区域之间的偏移量以及包含文本的置信度。由Tian等人提出的CTPN~\{tian2016detecting}和由Liao等人提出的Textboes~\{liao2017textboxes}是早期的代表性工作，前者由二阶段目标检测算法Fast-RCNN改进而来，后者基于SSD算法改进。
CTPN改造Fast-RCNN的区域提议网络（Region Proposal Network, RPN）模块，计了垂直方向上固定宽度的细长锚框，这种设计是考虑到文本行通常呈现细长的水平走向，而垂直方向上的变化相对较小。并引入双向长短期记忆网络（Bi-directional LSTM, BLSTM）有效地捕捉文本片段上下文。但CTPN的网络结构较为复杂，检测速度较慢，不属于如今OCR的主流文本检测算法。
Textboxes基于SSD做了两方面改进，一是将原本3*3的卷积核替换为一系列垂直长宽比为1*5的卷积核，以适应水平文本普遍较长的特性；其次，采用了更大长宽比的锚框。起架构图如图2.3所示。
![[image/图2.3.png]]为了应对输入图像可能出现的倾斜问题，学术界提出了任意角度文本检测算法，这类算法通常带有角度参数的锚框，通过输出不规则的四边形锚框来标记目标文本。代表性工作有Textboxes++~\{liao2018textboxes++}、EAST~{zhou2017east}、Most~{he2021most}等。
Textboxes++的改进思路类似于Textboxes，一是将原有1*5卷积核调整为3*5，二是引入了垂直高宽比为1、2、3、5、1/2、1/3、1/5的锚框，通过预测锚框的倾斜角度，更好地学习和不同角度文本的特征。其架构图如下：
![[论文图/图2.4.png]]上述任意角度文本检测解决了图像倾斜的问题，但自然场景中的文本还可能呈现弯曲的形状，如图2.5所示。传统的旋转矩形无法对目标文字进行标记，因此研究人员提出了一系列针对弯曲文本的检测算法。这类算法通常采用更为灵活的表示方式，如多边形或点序列来拟合弯曲文本的轮廓。
CRAFT~\{Character region awareness for text detection}摈弃了预定义锚框的概念，不假设文本实例具有特定的形状或方向，将文本框检测分解为两个更细粒度的任务，通过预测字符区域的置信度确定图像中字符的中心位置，通过预测相邻字符之间连接区域的置信度将独立的字符区域组合成完整的单词或文本行。
### 2.1.4基于分割的文本检测算法
基于回归的文本检测算法虽然提出了一些弯曲文本的解决方案，但在实际处理时难以得到平滑的文本包围曲线，且实时性能较差。从像素层面入手，研究者们提出了基于图像分割的文本分割方法，它将文本检测问题转化为语义分割问题或实例分割问题。其核心思想是将图像中的每个像素分类，得到可能的文本区域分布情况，再通过后处理得到文本区域的最小包围曲线。这种基于像素的方法对弯曲文本检测具有天然的优势，能处理任意形状的文本。
基于分割的文本检测算法包括PixelLink~\{deng2018pixellink}、PSENet、DB等。
PixelLink通过预测像素间的连接关系来分割文本实例。PixelLink模型并行地输出两个预测图：其一是标准的像素级文本/非文本语义分割图；其二是像素连接图，用以表征一个像素与其八邻域像素是否同属一个文本实例。在后处理阶段，通过对文本像素应用连通组件分析，并仅合并那些被预测为相互连接的像素对。但简单的像素级二分类（文本/非文本）很难区分开空间上非常接近的文本行或单词。所有文本像素都被标记为“文本”，会导致它们在后处理中被合并成一个大的连通组件，带来粘连问题。
针对粘连问题，PSENet提出了渐进式尺度扩展的策略。PSENet会为每个文本实例生成多个不同尺度的分割核心（kernels），这些核心是原始文本区域进行不同程度收缩后得到的。检测时，算法首先从最小、最不容易产生粘连的核心出发，通过广度优先搜索（BFS）逐步融合更大尺度的核心预测结果，逐渐将核心区域扩展到完整的文本实例。这种由小到大的渐进式扩展方式能够有效地分离靠得很近的文本，并且对文本尺寸和形状变化具有较好的鲁棒性。

## 2.1.5文本识别

文本识别任务是识别固定区域的文本内容，在OCR的两阶段方法中，此模块接收文本检测输出，将图像信息转换为文字信息。主流识别算法大多将文本识别视为从图像像素序列到字符序列的转换问题。此问题的挑战在于如何有效建模输出上下文信息的序列特征。
文本识别的主流模型架构有两种，第一类是CRNN(CNN+RNN+CTC) 架构，第二类是基于Attention的编码器-解码器架构。CRNN架构最初由shi等人提出，在传统使用卷积神经网络（CNN）提取图像特征的基础上，引入循环神经网络（RNN）学习序列的上下文信息，通过CTC模块解码序列结果。基于Attention的方法具有显式的编码器-解码器架构。在使用CNN作为编码器提取图像视觉特征后，通过自回归的编码器（如LSTM或RNN）逐个生成字符。通过解码器计算注意力分布，能自适应地关注相关图像区域。这种机制能更有效地识别不规则文本。
化学专利文献识别任务面临的一个核心挑战源于其输出空间的高维性。相比于拉丁字母，中文字符集数量庞大，模型需要在数千个类别中进行精确分类，这极大地增加了模型挖掘字符序列关系的难度。而CRNN模型因其相对简单的序列依赖假设和对齐机制，拥有推理速度快，鲁棒性高的特点，已经过业界广泛认证，适合对化学专利文本进行识别。
CRNN的网络结构如图xx所示。主要由三个层次构成：卷积层、循环层和转录层。其中，转录层使用主流的卷积结构，并移除顶部的全连接层，由多个卷积层、激活函数和池化层构成。它通过一系列卷积和池化操作将输入图像转为卷积特征图，随后将特征图的缩小为同样高度的特征向量。具体公式如下：
循环层接收CNN提取的特征序列，通过由两个LSTM构成的双向长短时记忆网络 (Bidirectional Long Short-Term Memory, BiLSTM)对其进行上下文建模，输出上下文特征序列。双向性使得模型在任何一帧进行预测时，能同时利用该帧过去和未来的上下文信息，从而极大地提高了文本预测的准确性。
转录层通过全连接网络和softmax，将循环层输出的预测值通过CTC解码为可变长度的文本标签序列。
![[论文图/图片2.5.png]]
## 2.2 光学化学结构识别
光学化学结构识别（OCSR）是指利用计算机视觉与机器学习技术，将图像形式的化学结构自动转换为机器可读、语义明确的化学数据格式的技术。早期的OCSR系统主要依赖于手工设计的规则和启发式算法，在处理复杂或多样化的化学结构时存在一定的局限性。近年来，随着深度学习的发展，尤其是基于Transformer架构的模型在图像识别与序列建模任务中展现出强大的建模能力，OCSR领域也迎来了显著的技术进步。基于Transformer的方法能够更好地捕捉图像中的长程依赖关系，并有效提升对复杂化学结构的识别准确率，成为当前研究的热点之一。
### 2.2.2 Transformer
Transformer模型由Vaswani\{vaswani2017attention}等人于2017年提出，通过其核心的自注意力机制（Self-Attention）彻底改变了序列处理领域，并迅速拓展到计算机视觉、自然语言处理等多个领域。与RNN不同，Transformer 架构完全基于自注意力和前馈网络构建，不引入任何循环或卷积操作，能够并行处理序列中的所有元素，Transformer的整体架构图如xx所示。
![[image/ModalNet-21.png]]
Transformer的核心构成部分为自注意力机制，其思想是为输入序列的每个元素计算一个权重分布，表示在生成输出序列的某个元素时，应该给予输入序列中哪些元素更多的关注。一个注意力函数可以描述为将一个查询 (Query, Q) 和一组键值对 (Key-Value, K-V) 映射到一个输出。输出是值的加权和，其中分配给每个值的权重由查询与相应键的兼容性函数计算得出。给定查询（Query）$Q\in \mathbb{R}^{n\times d_k}$、键（Key）$K\in \mathbb{R}^{m\times d_k}$和值（Value）$V\in \mathbb{R}^{m\times d_v}$，自注意力（Self-Attention）计算过程如下：


$$\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V$$
为使模型能够从不同的表征子空间（representation subspaces）并发地学习信息，Transformer引入了多头注意力机制。该机制首先通过独立的线性变换将原始的 $Q, K, V$ 投影 $h$ 次，获得 $h$ 组不同的查询、键和值 $(Q_i, K_i, V_i)$。随后，对这 $h$ 组投影并行地执行缩放点积注意力操作，产生 $h$ 个独立的输出向量。这些输出向量经过拼接（concatenation）后，再通过一次线性变换，融合成最终的多头注意力输出。$$[ \text{MultiHead}(Q, K, V) = \text{Concat}(\text{head}_1, \dots, \text{head}_h)W^O ]$$其中，每一个注意力头 $\text{head}_i$ 的计算如下：
$$\text{head}_i = \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)$$
在此，$W_i^Q \in \mathbb{R}^{d_{\text{model}} \times d_k}$，$W_i^K \in \mathbb{R}^{d_{\text{model}} \times d_k}$，$W_i^V \in \mathbb{R}^{d_{\text{model}} \times d_v}$ 
以及 $W^O \in \mathbb{R}^{hd_v \times d_{\text{model}}}$ 均为可学习的参数矩阵。通常设定 $d_k = d_v = d_{\text{model}}/h$。

Transformer模型主要由编码器（Encoder）和解码器（Decoder）两部分堆叠而成。
\begin{itemize} \item \textbf{编码器 (Encoder)}：由 $N$ 个结构相同的层堆叠而成。每一层均包含两个核心子层：其一为多头自注意力机制（Multi-Head Self-Attention Mechanism），其二为基于位置的逐点全连接前馈网络（Position-wise Fully Connected Feed-Forward Network）。每个子层的输出均经过残差连接（Residual Connection）与层归一化（Layer Normalization）处理。 \item \textbf{解码器 (Decoder)}：同样由 $N$ 个结构相同的层构成。除编码器层所含的两个子层外，解码器在其结构中额外集成了一个多头注意力子层，该子层负责处理编码器的输出表征。与编码器相似，解码器内的各子层亦辅以残差连接及层归一化。值得注意的是，解码器内部的自注意力子层经过特殊设计，采用了掩码机制（Masked Self-Attention），以确保在预测当前位置的输出时，模型无法访问到后续位置的信息，从而维持其自回归（auto-regressive）特性。 \end{itemize}
Transformer架构通过其全局建模与序列生成能力，为OCSR领域提供了新的方法论基础。在OCSR任务中，经典的Transformer架构包括编码器-解码器，适合图像到序列的转换任务。编码器负责处理从图像中提取特征，解码器将视觉特征转为序列化的化学表示。
# 3.智能化学信息提取系统需求分析
## 3.1系统需求概述
本系统旨在为化学研发与科研领域提供智能化信息提取解决方案。核心目标是通过集成先进算法与大语言模型（LLM），实现从多样化文献（专利、论文、实验记录）中高效、精准地提取化学结构、反应方程式、关键参数及文本信息，并支持深度内容理解与交互式分析。
在对药企研发团队、课题组师生进行深入访谈和问卷调研后，我们发现以下共性需求：
- 高精度化学实体识别：实现对图像中化学结构与反应方程式的高保真识别，并转换为标准化的计算机可读格式。
- 反应参数精确提取与关联：自动化提取关键反应参数（条件、产率等），并与相应反应步骤建立准确的语义链接。
- 高通量文献处理能力：支持对大规模文献集的批量处理，并具备初步的文档组织与管理辅助功能。
- 优化的用户交互界面：提供直观、高效的用户界面，提供提取信息可视化。
- LLM驱动的内容深度解析：运用大语言模型对Markdown文档进行语义分析，自动提炼核心摘要、关键化学实体及主要结论。
- OCR结果智能校正与优化：基于LLM对OCR文本进行上下文感知校正与专业化排版，提升化学信息提取的准确性。
- 交互式智能问答与决策辅助：集成LLM问答模块，支持用户就提取信息及相关知识进行自然语言垂询，提供智能化解答与科研建议。
## 3.2功能需求分析
### 3.2.1用户管理功能需求
为清晰阐述各微服务功能需求，本小节绘制了各个微服务的用例图。登录和注册用例图见图3.1。
（1）注册登录用例分析
注册与登录用例主要涉及用户的注册和登录两个功能。首次使用的用户需通过邮箱进行注册，系统将向该邮箱发送验证码以完成身份验证，确保邮箱的真实性与有效性。注册成功后，用户可使用注册时的邮箱和设置的密码进行登录。登录过程中，系统需对用户输入的信息进行校验，确保账户安全与访问合法性。
![[论文图/3.需求分析/图3.1 注册登录用例图.png]]### 3.2.2文档解析微服务功能需求
文档解析是信息提取流程的起点，其质量直接影响后续步骤的准确性与效率，用例图见图3.1。
![[论文图/3.需求分析/图3.1 文档解析用例图.png]]

（2）文档解析用例分析
文档解析涉及文档解析任务处理、历史解析结果查看、历史解析结果删除、解析结果优化和解析结果下载五个方面。该模块支持多种文档格式的自动解析，通过策略模式为不同类型的文档提供专门的解析器，并提供任务状态跟踪机制；用户可查询历史解析记录，并进行可视化查看与管理；系统亦支持对历史数据的手动删除操作，确保数据安全性与资源有效性；针对解析结果中存在的误差或格式问题，系统提供LLM接口对文本进行校正与排版；最终，用户可下载解析结果。
### 3.2.3 化学信息提取微服务功能需求
化学信息提取微服务是系统的核心，负责将文档中的文字结构图和化学反应图转为反应式，用例图见图3.2。
![[论文图/3.需求分析/图3.3.化学信息提取用例图.png]]
（3）化学信息提取用例分析：
化学信息提取涉及化学表达式识别，化学式解析、历史解析结果可视化、历史解析结果删除和解析结果下载五个方面。该模块支持通过视觉-语言大模型对图片分类，解析具有化学式和反应式的图片；对于解析结果，系统创建更用户友好的EXCEL表格，并对结果进行可视化，提供前端展示界；系统支持对历史数据的手动删除操作；最终，用户可下载完整解析结果。
### 3.2.4 智能问答功能需求
智能问答模块旨在结成多种大型语言模型API，允许用户通过前端获取语言大模型解答。用例图见图3.4。
![[论文图/3.需求分析/图3.4智能问答用例图.png]]
（4）智能问答用例分析：
智能回答系统用例图包括了问题输入、回答展示、历史对话管理和模型选择等功能。在该系统中，用户必须在使用前配置相应的API密钥，以便系统通过密钥调取相应大模型厂商的服务。智能问答系统核心功能包括问题输入、回答展示、模型选择和历史对话管理。系统需要接收用户在前端输入的问题，并将其封装后请求大模型API，在获取回答后，后端解析API相应并将答案清晰地渲染给用户；同时，用户可以对历史对话进行标题编辑和删除。
## 3.3非功能性需求分析：
非功能性需求是指不直接关联应用程序特定功能的要求，它们通常定义了系统如何运作，对系统的整体质量和用户体验至关重要。主要包括易用性、可靠性、安全性和性能需求等。
（1）易用性需求
易用性要求系统的界面应该简洁明了，便于用户快速上手和使用。系统应该提供直观的操作流程，减少用户的学习成本。系统应该为用户的操作提供及时，清晰的反馈，对于错误和异常情况，系统应提供错误提示和解决建议。
（2）可靠性需求
可靠性要求在规定的运行时间和特定的操作环境下，系统应稳定、连续地执行预定功能，并且应具备强大的错误预防能力和容错能力。
（3）安全性需求
安全性要求系统能抵御各种潜在威胁，保护数据完整性和机密性。本系统采用了基于UUID的会话认证机制，通过认证中间件验证令牌有效性，防止未经授权的数据访问和泄露。同时采用参数化查询，有效地防止SQL注入攻击。
（4）性能效率需求
本系统文档解析微服务基于LitServer实现，支持多GPU并行处理，能有效地管理和调度GPU资源，提高了服务的资源利用率、处理速度和并发处理能力。
（5）可扩展性需求
可拓展性需求要求系统能在不中断服务的情况下进行拓展。本系统采用微服务架构，将应用程序构建为一系列小型、自治服务。每个微服务都能独立完成服务，通过RESTful接口，处理HTTP通信，便于与其他系统集成。
# 4.系统概要设计
本章根据系统需求分析，结合实际业务场景进行概要设计。首先对系统整体架构进行设计，然后根据每个微服务的功能模块图对各微服务进行功能花费，最后从数据库进行分析和设计。
## 4.1系统整体架构设计
![[image/whiteboard_exported_image (2).png]]


基于微服务的智能化学信息提取系统的整体架构图如图xx所示，系统按照功能和职责划分为表现层、网关层、服务层、第三方服务和数据层。各层的具体描述如下：
1. 表现层
表现层是用户与系统直接交互的界面，本文的信息提取系统基于Vue.js和Tailwind CSS开发。Vue.js用于单页面应用（SPA），Tailwind CSS为负责页面组件的样式和外观。用户通过表现层进行交互，包括注册登录、文件处理、结果查看等。表现层处理用户交互行为，通过Axios库与网关层通信，实现了前后端分离的架构模式。
2. 网关层
网关层提供理论所有服务的接口，负责将请求发送到服务器的不同模块，基于Node.js的Express框架实现，负责请求路由、认证授权、请求转发和响应处理。本文的系统实现了基于UUID的会话管理机制，确保只有经过授权的请求才能访问后台服务。
3. 服务层
服务层是系统的核心业务逻辑层，负责处理具体的业务逻辑。本层包含了文档解析微服务，化学信息提取微服务和本地服务三大模块。其中本地服务模块负责处理用户请求、业务逻辑处理、数据库交互以及与远程服务器的通信。本系统其他两个微服务模块负责实现真正的业务需求处理。
化学式解析服务基于FastAPI框架，设计了基于进程池的任务调度机制，通过对不同的任务分配合适的进程池，能充分利用多GPU资源，实现任务的并行处理。文档解析微服务基于FastAPI和LitServer框架实现，LitServer是一个支持多GPU并行处理的高性能服务框架，能够有效地管理和调度GPU资源。通过策略模式为不同格式的文本调用专门的解析器，能有效避免不必要的模型调用，加快处理时间。各服务通过定义良好的RESTful API行通信，实现系统功能的分布式部署和横向拓展。
4. 第三方服务层
第三方服务指的是由外部厂商提供，可通过购买或免费使用来为体统提供额外功能的服务。本系统使用Nodemailer，通过QQ邮箱的SMTP服务发送验证码邮件。为实现智能问答服务，本系统集成了多家服务提供商提供的大语言模型API，支持用户配置自己的API密钥，动态选择不同的模型。
5. 数据层
数据层主要负责业务数据、文件、消息和缓存等各类资源的存储与访问。本系统使用关系型数据库MySQL来进行数据存取，为优化系统的可拓展性，本系统在开发时定义了系统的迁移用于数据库的版本控制，同时实现了数据库管理工具，通过数据库备份和恢复提供稳定的数据管理。

## 4.2系统功能模块设计
### 4.2.1系统功能概要设计
由系统需求分析和系统整体架构设计，我们可以将该系统分为如图xx所示的四个功能模块：文档解析微服务模块、化学信息提取微服务模块、智能问答模块和用户模块。其中，文档解析微服务和化学信息提取微服务是业务核心功能模块，负责实现多格式文档的解析和化学信息的提取。其他两个模块是系统重要的功能模块，提供用户管理和大模型调用功能。
下面通过类图和流程图介绍主要功能模块的详细设计。

![[论文图/图4.2.系统功能模块图png.png]]
### 4.2.1 文档解析微服务详细设计
文档解析微服务作为化学文献处理流程的起始节点，其核心职责是将多种输入格式的文档（例如 Word、Excel）统一转换为 Markdown 格式，以便于后续的结构化信息提取与分析。该微服务的类图如图 4.X 所示。
![[image/Pasted image 20250514220728.png]]
文档解析微服务采用了分层架构设计，自顶向下具体包含以下四个核心层次：

1. **API层 (API Layer):** 此层负责对外提供 RESTful API 接口，采用 FastAPI 框架实现。它处理所有传入的 HTTP 请求，并构建相应的 HTTP 响应。其中，`PredictRequest` 类作为数据传输对象 (DTO)，用于定义请求体的数据模型。FastAPI 应用实例接收来自表示层（客户端）的请求，并将这些请求及其参数路由至服务层的 `MineruAPI` 进行处理。
    
2. **服务层 (Service Layer):** MineruAPI 类是本微服务的核心业务逻辑实现单元。它封装了主要的文档解析流程，接收并处理由 API 层传递过来的请求。为完成多文档解析任务，MineruAPI依赖解析器层的DocumentParser抽象类及其具体实现，同时也集成了第三方库magicpdf以实现PDF的处理。
    
3. **解析器层 (Parser Layer):** 该层专注于具体文档格式的解析工作。DocumentParser是一个抽象基类，定义了所有文档解析器必须遵循的统一接口。WordParser和ExcelParser类分别继承此基类，并实现了针对 Word 和 Excel 文档类型的具体解析策略。此外，OnlineWordParser和OnlineExcelParser类通过组合本地解析器的方式，实现了对远程服务接口的兼容与适配。为动态创建和管理不同类型的解析器实例，本层采用了策略工厂模式 (Strategy Factory Pattern)，通过ParserFactory类实现。
    
4. **配置层 (Configuration Layer):** 配置层采用单例模式 (Singleton Pattern) 实现，为整个微服务的所有层级提供统一的配置信息管理。其管理的配置项包括但不限于服务器监听端口、解析结果输出目录路径以及所需第三方工具的参数等。
5. 
典型的用户交互与处理流程如下：用户在成功登录系统后，导航至文档解析功能界面。用户上传待处理的文档文件，系统随即将其发送至部署文档解析微服务的远程服务器。远程服务器接收文件后，启动解析流程，并将实时的处理状态与最终的解析结果返回给客户端。若文档解析成功完成，系统将允许用户下载包含 Markdown 文件的处理后文件夹，同时，相关的元数据（如文件名、解析时间、用户ID等）将被持久化存储到 MySQL 数据库中。用户可以在系统的历史处理记录中查看和管理所有已解析的文档，并可选择利用大型语言模型 (LLM) 对解析得到的 Markdown 内容进行进一步的优化、摘要生成或特定信息提取。该流程的详细图示如 4.Y 所示 (注：请替换 XX 为实际图号)。

![[论文图/图4.3.文档解析流程图.png]]

### 4.2.2化学信息提取微服务功能设计
化学信息提取微服务是化学文献处理流程中的核心组件，其主要功能是从文档（特别是专利文献）中包含化学结构式或分子式的图像中识别并提取化学信息，将其转换为 SMILES字符串，并通过OCR提取相关的化学反应条件。该微服务的功能类图如 4.Z 所示 
![[image/Pasted image 20250514235454.png]]
化学信息提取微服务的架构设计主要包含以下三个层次：

1. 表示层：表示层作为本微服务的统一外部接口，基于FastAPI框架构建，提供标准化的RESTful API。其核心职责在于接收并解析来自客户端的HTTP请求，例如包含待处理专利数据的请求，在进行初步校验后，通过其内部的\texttt{APIRouter}实例实现请求的精确路由与分发。随后，表示层调用应用层的\texttt{ServerCore}组件以执行核心业务逻辑，并将最终的处理结果或任务状态回传给客户端。整个FastAPI应用实例充当了服务配置管理与请求生命周期控制的入口点。
    
2. 应用层:应用层定位为连接表示层与领域层的核心枢纽，负责业务流程的编排、应用级资源管理以及任务调度。该层的核心组件\texttt{ServerCore}类作为微服务的中央控制器，不仅负责管理服务器级资源（如并发连接数限制、数据存储目录配置等），还与\texttt{ProcessorManager}组件紧密协作。\texttt{ProcessorManager}通过维护多个由领域层核心处理器（如\texttt{PatentProcessor}实例）组成的进程池，来实现计算资源的有效分配、动态负载均衡及任务的并行处理。当\texttt{ServerCore}从表示层接收到处理请求后，便会通过\texttt{ProcessorManager}将任务高效分派给领域层中合适的处理单元执行，这种设计有效降低了层间耦合度并支持灵活的调度策略。
    
3. 领域层: 领域层是本微服务的业务核心，完整封装了化学信息提取的全部核心业务逻辑、复杂算法模型以及相关的专门知识，致力于实现化学信息的精准识别、深度解析、高效提取与结构化表示。该层主要由若干关键业务组件构成：\texttt{MolCorefProcessor}（分子共指解析处理器）专注于从图像数据中识别化学结构并解析分子间的共指关系，它综合运用了\texttt{Moldetect}深度学习模型、\texttt{Molscribe}分子信息提取工具以及\texttt{PaddleOCR}和\texttt{Rxnscribe}进行化学式与反应式的识别；\texttt{PatentProcessor}（专利处理器）则针对专利文献的特有结构与信息提取需求进行设计，负责解析专利文档并协调调用如图形处理组件在内的其他领域服务；而\texttt{ResultManager}（结果管理器）则对各处理器生成的所有化学信息提取结果进行统一的收集、整理、格式化及管理，为后续的数据分析或应用提供结构化和规范化的数据支持。

用户操作流程如下：用户登录系统后，进入化学信息提取界面。用户选择并上传包含化学专利的文件夹，系统会将该文件夹打包后安全地传输至远程服务器。远程服务器上的化学信息提取微服务接收到请求后，开始执行图像识别、SMILES 转换和 OCR 提取等任务。处理完成后，用户可以在客户端界面查看处理状态并下载包含提取结果的文件夹。提取出的结构化化学数据（将被存储到 MySQL 数据库中。用户可以在历史处理记录中查询已提取的化学式及其对应的可视化结构图，并能够下载包含所有提取信息的、经过整理的 Excel 文档。该流程的详细图示如 4.W 所示 

![[论文图/图4.4.化学信息提取流程图.png]]
## 4.3数据库设计
本文的数据库设计采用了实体-关系（E-R）法，系统中主要实体包括用户、专利、化学信息、任务和对话。根据实体-关系法，本文建立的ER以如下xx所示。
![[论文图/图4.5.ER图.png]]
由数据库E-R图可知，系统中主要业务功能的数据库有9张，其中与文献提取相关的有专利表、反应式表、分子式表、任务表、源文件表。与用户相关的有用户表、API密钥表。与智能问答相关的有对话表、消息表。
下面介绍主要数据表。
（1）用户表（users）
用户表 (users) 存储了系统所有用户的核心认证信息和个人资料，是用户管理模块的基石。此表以用户ID (id)作为主键，唯一标识每位用户。关键字段包括用于登录验证的用户名 (username) 和密码（哈希）(password)，用于联络和账户恢复的电子邮件 (email)，以及定义用户权限级别的角色 (role)。此外，还记录了全名 (full_name)、头像路径 (avatar)、最后登录时间 (last_login) 及账户的创建时间 (created_at) 与更新时间 (updated_at)。
xxx
（2）API密钥表（api_keys）
API密钥表 (api_keys) 负责管理用户配置的用于访问第三方AI服务的认证凭证。该表以密钥ID (id) 为主键。通过外键用户ID (user_id) 与用户表 (users) 相关联，指明密钥的所属用户。核心字段包括用户实际填写的API密钥 (api_key)、所对应的AI模型名称 (model_name) 和可选的API基础URL (api_base_url)，这些信息使得系统能够代理用户请求AI服务。
xxx
（3）专利表（patents）
专利表 (patents) 用于存储用户上传的专利文献的元数据信息，是专利管理和分析功能的基础。此表以专利ID (id) 作为主键。通过外键用户ID (user_id) 关联到用户表 (users)，标识专利的上传者。重要字段包含用于识别专利的标题 (title) 和专利号 (patent_number)，存储专利原文的文件路径 (file_path)，以及记录文件大小 (file_size)、文件类型 (file_type) 和当前处理状态 (status) 的字段。
（4）化学式表（molecules）
化学式表 (molecules) 集中存储从专利文档中提取出的化学结构信息，为化学信息的检索与分析提供支持。该表以化学式ID (id) 作为主键。通过外键专利ID (patent_id) 关联到专利表 (patents)，指明该化学式来源于哪篇专利。关键数据包括表示化学结构的SMILES表示 (compound_smiles) 和InChI表示 (inchi)，化合物的通用名称 (compound_name)，提取置信度 (confidence)，以及原始图像在专利中的页码 (page_number) 和存储路径 (image_path)。
（5）反应表（reactions）
反应表 (reactions) 记录了从专利文献中识别和提取的化学反应信息。此表以反应ID (id) 作为主键。通过外键专利ID (patent_id) 关联到专利表 (patents)，表明反应所属的专利文档。核心字段包括描述反应物的反应物SMILES (reactants_smiles) 和描述产物的产物SMILES (product_smiles)，以及反应发生的具体条件 (conditions)。图像ID (image_id) 和图像路径 (image_path) 则关联了反应的原始图示。
(6)源文件表（origin_files）
源文件表 (origin_files) 管理用户上传的文档及其后续处理生成的各种文件信息。此表以文件ID (id)（通常为全局唯一标识符）作为主键。通过外键用户ID (user_id) 关联到用户表 (users)，标识文件的所有者。重要字段包括原始文件名 (original_filename)，以及处理后生成的Markdown路径 (markdown_path)、优化后的Markdown路径 (optimized_markdown_path) 和公式路径 (formulas_path)。此外，还记录了文件的状态 (status)、结果过期时间 (expires_at) 和下载统计 (download_count)，并建立了idx_pdf_files_expires_at和idx_pdf_files_user_id_status索引以提高查询效率。
（7）任务表（tasks）
任务表 (tasks) 用于跟踪和管理系统后台执行的专利提取任务信息，用于跟踪专利文档的化学式和反应提取过程。该表以任务ID (id) 作为主键。通过外键用户ID (user_id) 关联到用户表 (users)，表明任务发起者；并通过外键专利ID (patent_id) 关联到专利表 (patents)，指明任务处理的对象文档。核心字段包括任务的当前状态 (status)，执行进度 (progress)，相关的消息 (message) 或错误信息 (error)，以及存储任务结果的路径 (results_path)。
（8） 对话表（conversations）
对话表 (conversations) 存储用户与AI助手进行的对话会话信息，便于用户管理和回顾历史交流。该表以对话ID (id) 作为主键。通过外键用户ID (user_id) 关联到用户表 (users)，标识对话的参与用户。重要字段包括用户可自定义的对话标题 (title) 和该会话所使用的AI模型名称 (model_name)，以及会话的创建 (created_at)与更新时间 (updated_at)。
（9）消息表（messages）
消息表 (messages) 记录每个对话会话中用户与AI助手之间交互的具体消息条目。此表以消息ID (id) 作为主键。通过外键对话ID (conversation_id) 关联到对话表 (conversations)，将消息归属于特定的会话。关键字段包括消息的发送方角色 (role)（如用户或助手）和实际的文本内容 (content)，以及消息的创建时间 (created_at)。
# 6.智能化学信息提取系统实现
# 6.1系统开发环境介绍
操作系统方面，本地服务器使用Windows11专业版24H2作为操作系统，远程微服务使用Ubuntu 24.04.1 LTS。使用Visual Studio Code作为开发平台，主要的开发语言是python+JavaScript。
本系统使用关系型数据库MySQL，并使用Navicat作为管理工具。
系统架构方面，本系统前端基于Vue.js 3框架，后端使用Node.js和Express框架，使用FastAPI和LitServer构建远程服务。
本系统开发环境使用的组件组件与工具如下表xx所示：

| 属性   | 工具组件名称 | 软件/框架及版本                                    |
| ---- | ------ | ------------------------------------------- |
| 开发工具 | 操作系统   | Windows 11 (本地开发)，Ubuntu 20.04 (远程服务器)      |
|      | 开发平台   | Node.js 22.15.0                             |
|      | 开发语言   | JavaScript, Python 3.8+                     |
| 数据库  | 关系型数据库 | MySQL 5.7+                                  |
| 系统架构 | 前端架构   | Vue.js 3.2.37 SPA                           |
|      | 后端架构   | Express.js 4.18.1 RESTful API               |
|      | 微服务架构  | FastAPI (Python)，LotServer                  |
| 核心组件 | 前端框架   | Vue.js 3.2.37, Vue Router 4.1.2, Vuex 4.0.2 |
|      | UI框架   | Tailwind CSS 3.1.6                          |
|      | 网络通信   | Axios 0.27.2                                |
|      | 内容渲染   | v-md-editor 2.3.18                          |
| 专业服务 | PDF处理  | MinerU                                      |
|      | 化学式提取  | MolDetect, Rxnscribe                        |
## 6.2文档解析微服务实现
文档解析采用策略工厂模式，通过将不同解析任务分发给不同解析器，实现多格式文档解析。
### 6.2.1Word文档解析器实现
Word文档(.docx)本质上是一个ZIP压缩包，包含多个XML文件和资源文件。WordParser类是文档解析器的核心类，采用基于XML的结构化解析方法，按照文档的逻辑结构（段落、表格、图片等）进行处理，主要职责包括：文档格式统一、结构解析、样式转换和特殊元素处理等。

1. 文档格式统一：由于Word文档存在多种格式（.doc、.docx），为简化后续处理，解析器利用LibreOffice的命令行接口将所有格式统一转换为.docx。
2. 文档结构解析：文档结构解析采用基于XML的结构化解析方法，使用python-docx库加载文档，按照文档的逻辑结构（段落、表格、图片等）进行处理，主要算法如下：
def _parse_docx_content(self, file_path: str, output_dir: str, image_dir: str, file_name: str) -> str:
    """解析.docx文档内容"""
    doc = Document(file_path)  # 使用python-docx库加载文档
    md_lines = []  # 存储生成的Markdown行
    
    # 遍历文档中的所有元素
    for element in doc.element.body:
        if isinstance(element, CT_P):  # 段落处理
            # 处理段落文本和样式
            paragraph_text = self._get_paragraph_text(element)
            if paragraph_text:
                md_lines.append(self._format_paragraph(element, paragraph_text))
            
            # 处理段落中的图片
            self._process_images(element, doc, image_dir, md_lines)
            
        elif isinstance(element, CT_Tbl):  # 表格处理
            # 处理表格并添加到Markdown行
            md_lines.extend(self._process_table(element, doc))
    
    # 保存Markdown文件
    md_path = os.path.join(output_dir, f"{file_name}.md")
    with open(md_path, "w", encoding="utf-8") as f:
        f.writelines(md_lines)
    
    return md_path
3. 段落样式处理算法：段落算法核心是通过分析XML属性识别段落类型，然后应用相应的Markdown格式。这种方法能够准确识别和转换各种段落样式，包括标题、列表、引用等。
4. 文本格式处理算法：文本格式处理需要识别并转换文本中的格式标记（如粗体、斜体等）。系统采用基于XML节点分析的方法，识别并转换文本格式。通过- 逐个分析文本运行(run)元素，识别其格式属性，并使用相应的MarkDown格式包裹。
5. 图片提取与处理算法：Word文档(.docx)本质上是一个ZIP压缩包，包含多个XML文件和资源文件，图片作为独立资源存储在文档包内，通过关系(Relationship)机制与文档内容建立连接。系统首先通过XML解析图片关系ID，然后通过通过python-docx库提供的关系模型API获取实际图片资源，最后保存图片文件并建立MD索引。
def _process_images(self, paragraph, doc, image_dir: str, md_lines: List[str]) -> None:
    """处理段落中的图片"""
    # 确保图片目录存在
    os.makedirs(image_dir, exist_ok=True)
    
    # 查找段落中的所有图片
    for blip in paragraph.xpath(".//a:blip", namespaces=paragraph.nsmap):
        # 获取图片ID
        embed = blip.get("{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed")
        if not embed:
            continue
            
        # 从文档关系中获取图片
        image_part = doc.part.related_parts[embed]
        image_bytes = image_part.blob
        
        # 确定图片格式并保存
        image_ext = image_part.content_type.split("/")[-1]
        image_filename = f"image_{uuid.uuid4().hex}.{image_ext}"
        image_path = os.path.join(image_dir, image_filename)
        
        with open(image_path, "wb") as f:
            f.write(image_bytes)
            
        # 添加图片引用到Markdown
        md_lines.append(f"![Image]({os.path.join('images', image_filename)})\n\n")

6. 表格转换算法：解析器采用基于行列分析的方法，逐行处理表格内容，并转换为DM格式。

### 6.2.2Excel文档解析器
Excel文档(.xlsx)本质上是一个基于Office Open XML (OOXML)标准的ZIP压缩包，包含多个XML文件和资源文件。其核心数据结构包括：
1. **工作簿(Workbook)**：整个Excel文档的容器，包含多个工作表
2. **工作表(Worksheet)**：单个表格，包含行、列和单元格
3. **单元格(Cell)**：存储实际数据的基本单元，具有位置、值和格式等属性
Excel解析器工作流程如下：
def _parse_xlsx(self, file_path: str, output_dir: str, file_name: str) -> str:
    """解析.xlsx并转换为Markdown"""
    # 加载工作簿（使用data_only=True获取计算后的值）
    workbook = openpyxl.load_workbook(file_path, data_only=True)
    all_md_lines = []
    
    # 遍历所有工作表
    for sheet_name in workbook.sheetnames:
        ws = workbook[sheet_name]
        md_lines = [f"\n## {sheet_name}\n\n"]  # 添加工作表标题
        
        # 处理表头
        headers = self._get_headers(ws)
        md_lines.append(self._create_markdown_table_row(headers))
        md_lines.append(self._create_markdown_separator(len(headers)))
        
        # 处理数据行
        merged_ranges = ws.merged_cells.ranges
        for row in ws.iter_rows(min_row=2):  # 从第二行开始（跳过表头）
            row_data = self._process_row(row, merged_ranges)
            md_lines.append(self._create_markdown_table_row(row_data))
            
        all_md_lines.extend(md_lines)
    
    # 保存Markdown文件
    md_path = os.path.join(output_dir, f"{file_name}.md")
    with open(md_path, 'w', encoding='utf-8') as f:
        f.writelines(all_md_lines)
    
    return md_path


下面讲解主要算法：
4. 文档格式统一：与Word解析器类似，Excel解析器也使用LibreOffice将所有格式统一转换为.xlsx。
5. 工作表处理：Excel文档中往往包含多个表，系统需要识别并处理所有表。解析器使用openpyxl库的load_workbook函数加载Excel文件，通过递归处理所有表。
6. 单元格数据处理：工作表处理的核心是单元格数据处理，这涉及表头识别、数据行处理和合并单元格处理等多个环节。
### 6.2.3PDF解析器
PDF及其扫描件是文档解析的难点，对于此类文档，本系统使用深度学习模型Mineru提供PDF解析服务，并实现了多GPU并行处理机制提高处理效率。Mineru是一个基于多模块文档解析策略的文档解析算法，利用PDF-Extract-Kit库中继承的各种开源模型实现文本分割和内容提取。
Mineru框架的处理流程如下图所示：

具体而言，Mineru的处理工作流程分为四个阶段：
1. 文档预处理：此阶段使用 PyMuPDF包读取 PDF 文件，过滤出无法处理的文件（例如加密文件），并提取 PDF 元数据，包括文档的可解析性（分为可解析 PDF 和扫描 PDF）、语言类型和页面尺寸。
2. 文档内容解析。此阶段采用 PDF-Extract-Kit，一个PDF 文档提取算法库，解析关键文档内容。它首先进行布局分析，包括布局和公式检测。然后针对不同区域应用不同的识别器：OCR用于文本和标题，公式识别用于公式，以及表格识别用于表格。
3. 文档内容后处理。基于第二阶段的输出，此阶段移除无效区域，根据区域定位信息拼接内容，并最终获取不同文档区域的定位、内容和排序信息。
4. 格式转换。基于文档后处理的结果，生成 Markdown 等各种格式，供后续使用。
 本系统使用的模型见下表：


| 任务类型 | 任务描述                             | 模型                      |
| ---- | -------------------------------- | ----------------------- |
| 布局检测 | 定位文档中不同元素位置：包含图像、表格、文本、标题、公式等    | doclayout_yolo          |
| 公式检测 | 定位文档中公式位置：包含行内公式和行间公式            | yolo_v8_mfd             |
| 公式识别 | 识别公式图像为latex源码                   | unimernet_small         |
| OCR  | 提取图像中的文本内容（包括定位和识别）              | PP-OCRv4_server_rec_doc |
| 表格识别 | 识别表格图像为对应源码（Latex/HTML/Markdown） | rapid_table             |

### 6.2.4并行处理框架
为提高文档处理效率，该微服务采用了基于LitServer的多GPU并行处理架构，这是其高性能处理的核心。LitServer是一个专门设计用于支持多GPU并行处理的高性能服务框架，能够有效地管理和调度GPU资源。该微服务的任务处理流程主要包括以下步骤：
1. 工作进程创建：文档解析微服务的工作进程通过LitServer创建，通过workers_per_device参数，为每个GPU设备创建多个工作进程，避免资源竞争。在每个工作进程启动时，mineru会进行设备设置和模型初始化，使用ModelSingleton确保每个进程只加载一次模型，避免重复加载。
2. 请求解码：当请求到达时，LitServer会解码请求数据，将任务分发给空闲的工作进程。任务完成后，工作进程不会被销毁，而是返回到池中等待下一个任务，实现了进程复用。
3. 资源清理：每次任务处理完成后，该进程会进行资源清理，释放内存。
这样的设计减少了进程创建和销毁开销，实现了进程级并行，提高了系统的响应速度和资源利用率。





本章主要介绍了智能化学信息提取系统的具体实现过程，包括第五章所述的功能模块和网站基础模块的实现，是基于前述文档解析算法和化学信息提取算法实现的完整应用系统。

## 6.1注册登录实现
对于首次使用本系统的用户，需通过邮箱注册以完成身份认证。该模块采用邮箱验证机制，以确保用户身份的真实性与系统安全性。注册界面如图6-1所示。
![[论文图/6.实现/图1.用户注册.png]]
用户填写注册信息后，系统将向其提供的邮箱发送一封包含验证码的邮件，验证码有效期为10分钟。该机制可有效防止恶意注册行为。验证码邮件示例如图6-2所示。

完成注册后，用户可以通过网站首页登录系统，登录界面如下图xx所示
![[论文图/6.实现/图3.用户登录.png]]
## 6.2用户管理模块实现
### 6.2.1用户个人信息修改功能实现
用户成功登录系统后，可通过点击界面左上角的“设置”按钮进入用户设置页面。在此界面中，用户可对个人基本信息进行编辑，包括姓名、密码等字段，并支持配置微服务远程服务器地址及其连接测试功能。用户信息修改界面如图6-4所示。
![[论文图/6.实现/图4.用户个人信息修改.png]]
### 6.2.2API密钥修改功能实现
为了调用第三方AI对话服务，用户在使用系统内置AI聊天功能前，需在设置界面中配置由AI服务提供商颁发的API密钥。该模块支持用户添加、删除及编辑多个API密钥，API密钥管理API管理首页如图6-5所示。
![[论文图/6.实现/图5.API密钥管理.png]]
当用户想要添加密钥时，用户可以点击添加API密钥按钮，选择相应AI服务商，填写密钥和URL进行添加，其界面如下图。
![[论文图/6.实现/图6.API密钥管理_添加.png]]
### 6.3智能问答模块实现
添加API密钥后，用户点击导航栏AI聊天即可使用智能问答服务，进入智能问答模块首页时，用户可以看到左上角的侧边栏按钮和新建对话按钮，用户可以点击查看侧边栏和新建对话；位于首页中间的是用户提示框，提示框下方是输入栏。其界面如下图xx所示。
![[论文图/6.实现/图6.智能问答首页.png]]
点击新建对话后，用户需要选择模型。模型选择页有模型和简要介绍，对已配置/未配置服务做了颜色区分。用户可以通过搜索框对厂商进行关键词搜索，也可以滑动选择厂商和模型。界面如下图xx所示。
![[论文图/6.实现/图7.模型选择页.png]]
在对话中，用户可以与智能助手对话，点击气泡右上角的复制按钮，可以复制本次回复所有内容。选择支持视觉理解的模型后（如Qwen-VL系列），用户可以发送图片。
![[论文图/6.实现/图8.聊天页.png]]
点击侧边栏按钮，用户可以查看历史对话、修改历史对话名和删除历史对话。历史对话名默认为“新对话”，当用户发送消息后，根据初始发送消息修改对话名。
![[论文图/6.实现/图9.侧边栏.png]]
## 6.4 PDF转换首页
PDF转换模块通过文档解析微服务，实现多种文档处理格式的转换服务。用户可以在此模块将多种格式的文档转换为MarkDown文档，并查看和管理处理结果，使用AI优化。PDF转换模块首页如下图。
![[论文图/6.实现/图10.PDF转换首页.png]]
用户可以在历史转换记录中查看已处理文件，并对文件操作。
